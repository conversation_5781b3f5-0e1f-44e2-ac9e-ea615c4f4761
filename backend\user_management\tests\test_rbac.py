from django.test import TestCase, Client
from django.contrib.auth.models import User, Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from user_management.models import Role, UserProfile, PermissionCategory
from user_management.middleware import clear_user_cache
import json


class RBACModelTests(TestCase):
    """Test RBAC models functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.admin_group = Group.objects.create(name='Admin')
        self.staff_group = Group.objects.create(name='Staff')
        
    def test_role_creation(self):
        """Test Role model creation"""
        role = Role.objects.create(
            group=self.admin_group,
            description='Administrator role',
            is_active=True
        )
        self.assertEqual(role.name, 'Admin')
        self.assertEqual(role.description, 'Administrator role')
        self.assertTrue(role.is_active)
        
    def test_user_profile_creation(self):
        """Test UserProfile creation"""
        profile = UserProfile.objects.create(
            user=self.user,
            department='IT',
            phone='************',
            employee_id='EMP001'
        )
        self.assertEqual(profile.user, self.user)
        self.assertEqual(profile.department, 'IT')
        self.assertEqual(profile.full_name, 'testuser')
        
    def test_permission_category_creation(self):
        """Test PermissionCategory creation"""
        category = PermissionCategory.objects.create(
            name='User Management',
            description='Permissions for user management'
        )
        self.assertEqual(category.name, 'User Management')


class RBACMiddlewareTests(TestCase):
    """Test RBAC middleware functionality"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.admin_group = Group.objects.create(name='Admin')
        self.user.groups.add(self.admin_group)
        
    def test_cache_clearing(self):
        """Test cache clearing functionality"""
        # This would test the cache clearing functions
        clear_user_cache(self.user.id)
        # In a real test, you'd verify the cache was cleared
        self.assertTrue(True)  # Placeholder


class RBACAPITests(APITestCase):
    """Test RBAC API endpoints"""
    
    def setUp(self):
        self.client = APIClient()
        
        # Create test users
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='adminpass123',
            is_staff=True
        )
        
        self.regular_user = User.objects.create_user(
            username='user',
            email='<EMAIL>',
            password='userpass123'
        )
        
        # Create groups and roles
        self.admin_group = Group.objects.create(name='Admin')
        self.staff_group = Group.objects.create(name='Staff')
        
        self.admin_role = Role.objects.create(
            group=self.admin_group,
            description='Administrator role'
        )
        
        self.staff_role = Role.objects.create(
            group=self.staff_group,
            description='Staff role'
        )
        
        # Assign roles
        self.admin_user.groups.add(self.admin_group)
        self.regular_user.groups.add(self.staff_group)
        
        # Add some permissions
        content_type = ContentType.objects.get_for_model(User)
        view_user_perm = Permission.objects.get_or_create(
            codename='view_user',
            name='Can view user',
            content_type=content_type
        )[0]
        
        self.admin_group.permissions.add(view_user_perm)
        
    def get_jwt_token(self, user):
        """Helper to get JWT token for user"""
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)
        
    def test_current_user_endpoint_with_rbac(self):
        """Test current user endpoint returns RBAC information"""
        token = self.get_jwt_token(self.admin_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get('/api/user/me/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertIn('role_names', data)
        self.assertIn('roles', data)
        self.assertIn('permissions', data)
        self.assertIn('is_admin', data)
        self.assertIn('Admin', data['role_names'])
        
    def test_rbac_roles_endpoint(self):
        """Test RBAC roles endpoint"""
        token = self.get_jwt_token(self.admin_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get('/api/user/rbac-roles/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertIsInstance(data, list)
        
    def test_role_assignment(self):
        """Test role assignment functionality"""
        token = self.get_jwt_token(self.admin_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # Test assigning users to role
        response = self.client.post(
            f'/api/user/rbac-roles/{self.staff_role.id}/assign_users/',
            {'user_ids': [self.regular_user.id]},
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
    def test_permission_categories_endpoint(self):
        """Test permission categories endpoint"""
        token = self.get_jwt_token(self.admin_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get('/api/user/permission-categories/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
    def test_user_profiles_endpoint(self):
        """Test user profiles endpoint"""
        token = self.get_jwt_token(self.admin_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # Create a profile for the user
        UserProfile.objects.create(
            user=self.admin_user,
            department='IT',
            phone='************'
        )
        
        response = self.client.get('/api/user/profiles/me/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertEqual(data['department'], 'IT')
        
    def test_rbac_utility_endpoint(self):
        """Test RBAC utility endpoint"""
        token = self.get_jwt_token(self.admin_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get('/api/user/rbac/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertIn('user_id', data)
        self.assertIn('roles', data)
        self.assertIn('permissions', data)
        self.assertIn('is_admin', data)
        
    def test_unauthorized_access(self):
        """Test that unauthorized users can't access protected endpoints"""
        # Test without authentication
        response = self.client.get('/api/user/rbac-roles/')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # Test with regular user trying to access admin endpoints
        token = self.get_jwt_token(self.regular_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get('/api/user/permission-categories/')
        # This should be forbidden for regular users
        self.assertIn(response.status_code, [status.HTTP_403_FORBIDDEN, status.HTTP_401_UNAUTHORIZED])


class RBACPermissionTests(TestCase):
    """Test RBAC permission classes"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.admin_group = Group.objects.create(name='Admin')
        self.staff_group = Group.objects.create(name='Staff')
        
    def test_admin_permission(self):
        """Test admin permission checking using dynamic hierarchy"""
        from user_management.dynamic_permissions import check_user_has_role_level

        # User without admin role
        self.assertFalse(check_user_has_role_level(self.user, 90))

        # Add admin role
        self.user.groups.add(self.admin_group)
        # Note: In a real test, we'd need to create RoleConfiguration objects
        # For now, we test the group membership
        self.assertTrue(self.user.groups.filter(name='Admin').exists())

    def test_staff_permission(self):
        """Test staff permission checking using dynamic hierarchy"""
        from user_management.dynamic_permissions import check_user_has_role_level

        # User without staff role
        self.assertFalse(check_user_has_role_level(self.user, 30))

        # Add staff role
        self.user.groups.add(self.staff_group)
        # Note: In a real test, we'd need to create RoleConfiguration objects
        # For now, we test the group membership
        self.assertTrue(self.user.groups.filter(name='Staff').exists())


class RBACIntegrationTests(APITestCase):
    """Integration tests for the complete RBAC system"""
    
    def setUp(self):
        self.client = APIClient()
        
        # Create a complete RBAC setup
        self.super_admin = User.objects.create_user(
            username='superadmin',
            email='<EMAIL>',
            password='superpass123',
            is_superuser=True
        )
        
        self.admin = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='adminpass123'
        )
        
        self.manager = User.objects.create_user(
            username='manager',
            email='<EMAIL>',
            password='managerpass123'
        )
        
        self.staff = User.objects.create_user(
            username='staff',
            email='<EMAIL>',
            password='staffpass123'
        )
        
        # Create roles
        self.admin_group = Group.objects.create(name='Admin')
        self.manager_group = Group.objects.create(name='Manager')
        self.staff_group = Group.objects.create(name='Staff')
        
        # Assign roles
        self.admin.groups.add(self.admin_group)
        self.manager.groups.add(self.manager_group)
        self.staff.groups.add(self.staff_group)
        
    def test_role_hierarchy(self):
        """Test that role hierarchy works correctly"""
        # Super admin should have access to everything
        token = self.get_jwt_token(self.super_admin)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get('/api/user/rbac/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertTrue(data['is_superuser'])
        
    def get_jwt_token(self, user):
        """Helper to get JWT token for user"""
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)
