"""
Database configuration for Online Application Portal
Supports both Docker and local development environments
"""

import os
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

def get_database_config():
    """
    Get database configuration based on environment
    """
    
    # Check if we're using Docker environment
    use_docker = os.getenv('USE_DOCKER', 'false').lower() == 'true'
    
    if use_docker:
        # Docker configuration
        # Prioritize DB_* variables for backward compatibility, fallback to POSTGRES_*
        return {
            'default': {
                'ENGINE': os.getenv('DB_ENGINE', 'django.db.backends.postgresql'),
                'NAME': os.getenv('DB_NAME', os.getenv('POSTGRES_DB', 'ApplicationPortal')),
                'USER': os.getenv('DB_USER', os.getenv('POSTGRES_USER', 'postgres')),
                'PASSWORD': os.getenv('DB_PASSWORD', os.getenv('POSTGRES_PASSWORD', 'admin')),
                'HOST': os.getenv('DB_HOST', os.getenv('POSTGRES_HOST', 'postgres')),  # Docker service name
                'PORT': os.getenv('DB_PORT', os.getenv('POSTGRES_PORT', '5432')),
                'OPTIONS': {
                    'connect_timeout': 60,
                },
                'CONN_MAX_AGE': 60,
                'CONN_HEALTH_CHECKS': True,
            }
        }
    else:
        # Local development configuration
        # Prioritize DB_* variables for backward compatibility, fallback to POSTGRES_*
        return {
            'default': {
                'ENGINE': os.getenv('DB_ENGINE', 'django.db.backends.postgresql'),
                'NAME': os.getenv('DB_NAME', os.getenv('POSTGRES_DB', 'ApplicationPortal')),
                'USER': os.getenv('DB_USER', os.getenv('POSTGRES_USER', 'postgres')),
                'PASSWORD': os.getenv('DB_PASSWORD', os.getenv('POSTGRES_PASSWORD', 'admin')),
                'HOST': os.getenv('DB_HOST', os.getenv('POSTGRES_HOST', 'localhost')),
                'PORT': os.getenv('DB_PORT', os.getenv('POSTGRES_PORT', '5432')),
                'OPTIONS': {
                    'connect_timeout': 60,
                },
                'CONN_MAX_AGE': 60,
                'CONN_HEALTH_CHECKS': True,
            }
        }

def get_cache_config():
    """
    Get cache configuration - fallback to local memory cache if Redis not available
    """

    use_redis = os.getenv('USE_REDIS', 'false').lower() == 'true'

    if use_redis:
        try:
            import django_redis
            use_docker = os.getenv('USE_DOCKER', 'false').lower() == 'true'
            redis_host = 'redis' if use_docker else 'localhost'

            return {
                'default': {
                    'BACKEND': 'django_redis.cache.RedisCache',
                    'LOCATION': f"redis://:{os.getenv('REDIS_PASSWORD', 'redis_password123')}@{redis_host}:6379/1",
                    'OPTIONS': {
                        'CLIENT_CLASS': 'django_redis.client.DefaultClient',
                    },
                    'KEY_PREFIX': 'online_portal',
                    'TIMEOUT': 300,  # 5 minutes default timeout
                }
            }
        except ImportError:
            print("Warning: django-redis not installed, falling back to local memory cache")

    # Fallback to local memory cache
    return {
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'online-portal-cache',
            'TIMEOUT': 300,
            'OPTIONS': {
                'MAX_ENTRIES': 1000,
            }
        }
    }

# Environment-specific settings
DATABASE_ROUTERS = []

# Database connection pool settings
DATABASE_POOL_SETTINGS = {
    'MAX_CONNS': 20,
    'MIN_CONNS': 5,
}

# Logging configuration for database
DATABASE_LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'db_log_file': {
            'level': 'DEBUG',
            'class': 'logging.FileHandler',
            'filename': BASE_DIR / 'logs' / 'database.log',
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'django.db.backends': {
            'handlers': ['db_log_file'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
}
