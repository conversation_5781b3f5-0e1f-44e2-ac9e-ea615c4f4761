#!/usr/bin/env python
"""
Validation script to check that hardcoded RBAC has been successfully removed
"""

import os
import re
import glob
from pathlib import Path

def find_hardcoded_roles_in_file(file_path):
    """Find hardcoded role references in a file"""
    hardcoded_patterns = [
        r"required_roles\s*=\s*\[.*?['\"]Administrator['\"].*?\]",
        r"required_roles\s*=\s*\[.*?['\"]Super Admin['\"].*?\]",
        r"required_roles\s*=\s*\[.*?['\"]Main Registrar['\"].*?\]",
        r"required_roles\s*=\s*\[.*?['\"]Department Head['\"].*?\]",
        r"PROTECTED_URLS\s*=\s*{",
        r"name__in=\[.*?['\"]Administrator['\"].*?\]",
        r"groups\.filter\(name__in=\[.*?['\"]Admin['\"].*?\]\)",
        r"hasAnyRole\(\[.*?UserRoles\.",
    ]
    
    issues = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        for i, line in enumerate(content.split('\n'), 1):
            for pattern in hardcoded_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    issues.append({
                        'line': i,
                        'content': line.strip(),
                        'pattern': pattern
                    })
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
    
    return issues

def check_dynamic_rbac_implementation():
    """Check if dynamic RBAC components are properly implemented"""
    required_files = [
        'user_management/models.py',
        'user_management/dynamic_permissions.py',
        'user_management/dynamic_middleware.py',
        'user_management/management/commands/migrate_to_dynamic_rbac.py',
        '../frontend/src/hooks/usePermissions.ts',
        '../frontend/src/components/RoleBasedRoute.tsx'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    return missing_files

def check_for_dynamic_features():
    """Check if dynamic RBAC features are implemented"""
    features_to_check = {
        'user_management/models.py': [
            'class RoleConfiguration',
            'class URLProtectionRule',
            'hierarchy_level',
            'get_all_permissions'
        ],
        'user_management/dynamic_permissions.py': [
            'DynamicRolePermission',
            'check_user_has_role_level',
            'ResourceBasedPermission',
            'URLBasedPermission'
        ],
        'user_management/dynamic_middleware.py': [
            'DynamicRBACMiddleware',
            'URLProtectionRule',
            'hierarchy_level'
        ],
        '../frontend/src/hooks/usePermissions.ts': [
            'hasHierarchyLevel',
            'requiredHierarchyLevel',
            'hierarchy_level'
        ]
    }
    
    missing_features = {}
    
    for file_path, features in features_to_check.items():
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                missing_in_file = []
                for feature in features:
                    if feature not in content:
                        missing_in_file.append(feature)
                
                if missing_in_file:
                    missing_features[file_path] = missing_in_file
            except Exception as e:
                missing_features[file_path] = [f"Error reading file: {e}"]
    
    return missing_features

def scan_codebase():
    """Scan the entire codebase for hardcoded role references"""
    print("🔍 Scanning codebase for hardcoded role-based access control...")
    
    # Define file patterns to scan
    backend_patterns = [
        '**/*.py',
        '**/views.py',
        '**/permissions.py',
        '**/middleware.py'
    ]

    frontend_patterns = [
        '../frontend/src/**/*.ts',
        '../frontend/src/**/*.tsx',
        '../frontend/src/**/*.js',
        '../frontend/src/**/*.jsx'
    ]
    
    all_issues = {}
    
    # Scan backend files
    print("\n📁 Scanning backend files...")
    for pattern in backend_patterns:
        for file_path in glob.glob(pattern, recursive=True):
            if os.path.isfile(file_path):
                issues = find_hardcoded_roles_in_file(file_path)
                if issues:
                    all_issues[file_path] = issues
    
    # Scan frontend files
    print("📁 Scanning frontend files...")
    for pattern in frontend_patterns:
        for file_path in glob.glob(pattern, recursive=True):
            if os.path.isfile(file_path):
                issues = find_hardcoded_roles_in_file(file_path)
                if issues:
                    all_issues[file_path] = issues
    
    return all_issues

def validate_rbac_removal():
    """Main validation function"""
    print("🚀 Validating RBAC Hardcode Removal")
    print("=" * 50)
    
    # Check if dynamic RBAC files exist
    print("\n1️⃣ Checking dynamic RBAC implementation...")
    missing_files = check_dynamic_rbac_implementation()
    
    if missing_files:
        print("❌ Missing dynamic RBAC files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
    else:
        print("✅ All dynamic RBAC files are present")
    
    # Check for dynamic features
    print("\n2️⃣ Checking dynamic RBAC features...")
    missing_features = check_for_dynamic_features()
    
    if missing_features:
        print("❌ Missing dynamic RBAC features:")
        for file_path, features in missing_features.items():
            print(f"   📄 {file_path}:")
            for feature in features:
                print(f"      - {feature}")
    else:
        print("✅ All dynamic RBAC features are implemented")
    
    # Scan for remaining hardcoded roles
    print("\n3️⃣ Scanning for remaining hardcoded roles...")
    hardcoded_issues = scan_codebase()
    
    if hardcoded_issues:
        print("⚠️  Found potential hardcoded role references:")
        for file_path, issues in hardcoded_issues.items():
            print(f"\n   📄 {file_path}:")
            for issue in issues[:3]:  # Show first 3 issues per file
                print(f"      Line {issue['line']}: {issue['content'][:80]}...")
            if len(issues) > 3:
                print(f"      ... and {len(issues) - 3} more issues")
    else:
        print("✅ No hardcoded role references found")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 VALIDATION SUMMARY")
    print("=" * 50)
    
    total_issues = len(missing_files) + len(missing_features) + len(hardcoded_issues)
    
    if total_issues == 0:
        print("🎉 SUCCESS! Hardcoded RBAC has been successfully removed!")
        print("\n✅ Achievements:")
        print("   - Dynamic role configuration system implemented")
        print("   - Hardcoded role arrays replaced with hierarchy levels")
        print("   - URL protection rules made configurable")
        print("   - Frontend updated to use dynamic role checking")
        print("   - Backward compatibility maintained")
        print("   - Security improved with flexible access control")
        
        return True
    else:
        print(f"⚠️  Found {total_issues} issues that need attention:")
        if missing_files:
            print(f"   - {len(missing_files)} missing implementation files")
        if missing_features:
            print(f"   - {len(missing_features)} files missing dynamic features")
        if hardcoded_issues:
            print(f"   - {len(hardcoded_issues)} files with potential hardcoded roles")
        
        print("\n🔧 Next steps:")
        print("   1. Implement missing dynamic RBAC files")
        print("   2. Add missing dynamic features")
        print("   3. Replace remaining hardcoded role references")
        print("   4. Test the dynamic system thoroughly")
        
        return False

if __name__ == "__main__":
    success = validate_rbac_removal()
    exit(0 if success else 1)
