from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission, User
from django.contrib.contenttypes.models import ContentType
from user_management.models import Role, PermissionCategory, RoleConfiguration, URLProtectionRule


class Command(BaseCommand):
    help = 'Set up default RBAC roles and permissions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset all roles and permissions before creating new ones',
        )

    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('Resetting existing roles and permissions...')
            Role.objects.all().delete()
            Group.objects.all().delete()
            PermissionCategory.objects.all().delete()

        self.stdout.write('Setting up RBAC system...')

        # Create permission categories
        categories = [
            {'name': 'User Management', 'description': 'Permissions related to user management'},
            {'name': 'Academic Management', 'description': 'Permissions related to academic data'},
            {'name': 'System Administration', 'description': 'System-level permissions'},
            {'name': 'Content Management', 'description': 'Content and communication permissions'},
            {'name': 'Registration Management', 'description': 'Student registration and application permissions'},
        ]

        for cat_data in categories:
            category, created = PermissionCategory.objects.get_or_create(
                name=cat_data['name'],
                defaults={'description': cat_data['description']}
            )
            if created:
                self.stdout.write(f'Created permission category: {category.name}')

        # Define roles and their permissions
        roles_config = {
            'Super Admin': {
                'description': 'Highest level access with full system control',
                'permissions': [
                    # User management
                    'auth.add_user', 'auth.change_user', 'auth.delete_user', 'auth.view_user',
                    'auth.add_group', 'auth.change_group', 'auth.delete_group', 'auth.view_group',
                    'auth.add_permission', 'auth.change_permission', 'auth.delete_permission', 'auth.view_permission',

                    # Registration management
                    'registration.add_applicantprogramselection', 'registration.change_applicantprogramselection',
                    'registration.delete_applicantprogramselection', 'registration.view_applicantprogramselection',

                    # Official certificates
                    'official.add_official', 'official.change_official', 'official.delete_official', 'official.view_official',

                    # Setup models
                    'college.add_college', 'college.change_college', 'college.delete_college', 'college.view_college',
                    'department.add_department', 'department.change_department', 'department.delete_department', 'department.view_department',
                    'program.add_program', 'program.change_program', 'program.delete_program', 'program.view_program',
                ]
            },
            'Administrator': {
                'description': 'High-level administrative access with most system features',
                'permissions': [
                    # User management (limited)
                    'auth.add_user', 'auth.change_user', 'auth.view_user',
                    'auth.view_group', 'auth.view_permission',

                    # Registration management
                    'registration.add_applicantprogramselection', 'registration.change_applicantprogramselection',
                    'registration.view_applicantprogramselection',

                    # Official certificates
                    'official.add_official', 'official.change_official', 'official.view_official',

                    # Setup models
                    'college.add_college', 'college.change_college', 'college.view_college',
                    'department.add_department', 'department.change_department', 'department.view_department',
                    'program.add_program', 'program.change_program', 'program.view_program',
                ]
            },
            'Main Registrar': {
                'description': 'Senior registrar with oversight responsibilities and full registration access',
                'permissions': [
                    # Limited user management
                    'auth.view_user', 'auth.view_group',

                    # Registration management
                    'registration.change_applicantprogramselection', 'registration.view_applicantprogramselection',

                    # Official certificates
                    'official.change_official', 'official.view_official',

                    # Setup models (view and change)
                    'college.change_college', 'college.view_college',
                    'department.change_department', 'department.view_department',
                    'program.change_program', 'program.view_program',
                ]
            },
            'Registrar Officer': {
                'description': 'Standard registrar with operational duties and limited approval authority',
                'permissions': [
                    # Basic user viewing
                    'auth.view_user',

                    # Registration management
                    'registration.view_applicantprogramselection',

                    # Official certificates
                    'official.view_official',

                    # Setup models (view only)
                    'college.view_college',
                    'department.view_department',
                    'program.view_program',
                ]
            },
            'Department Head': {
                'description': 'Academic department leadership with department-specific access and management',
                'permissions': [
                    # View only permissions
                    'auth.view_user',
                    'registration.view_applicantprogramselection',
                    'official.view_official',
                    'college.view_college',
                    'department.view_department',
                    'program.view_program',
                ]
            },
            'Applicant': {
                'description': 'End users applying to the university with access to their own application data',
                'permissions': [
                    # Basic permissions for applicants
                    'registration.view_applicantprogramselection',  # Only their own data
                ]
            },
        }

        # Create roles and assign permissions
        for role_name, role_config in roles_config.items():
            # Create or get the group
            group, created = Group.objects.get_or_create(name=role_name)
            if created:
                self.stdout.write(f'Created group: {role_name}')

            # Create or get the role
            role, created = Role.objects.get_or_create(
                group=group,
                defaults={
                    'description': role_config['description'],
                    'is_active': True
                }
            )
            if created:
                self.stdout.write(f'Created role: {role_name}')

            # Create or update RoleConfiguration for dynamic RBAC
            role_config_obj, config_created = RoleConfiguration.objects.get_or_create(
                name=role_name.lower().replace(' ', '_'),
                defaults={
                    'display_name': role_name,
                    'description': role_config['description'],
                    'hierarchy_level': self._get_hierarchy_level(role_name),
                    'is_system_role': True,
                    'is_active': True
                }
            )
            if config_created:
                self.stdout.write(f'Created RoleConfiguration: {role_name}')

            # Assign permissions
            permissions_to_add = []
            for perm_codename in role_config['permissions']:
                try:
                    app_label, codename = perm_codename.split('.')
                    permission = Permission.objects.get(
                        codename=codename,
                        content_type__app_label=app_label
                    )
                    permissions_to_add.append(permission)
                except Permission.DoesNotExist:
                    self.stdout.write(
                        self.style.WARNING(f'Permission not found: {perm_codename}')
                    )
                except ValueError:
                    self.stdout.write(
                        self.style.ERROR(f'Invalid permission format: {perm_codename}')
                    )

            group.permissions.set(permissions_to_add)
            role_config_obj.permissions.set(permissions_to_add)
            self.stdout.write(f'Assigned {len(permissions_to_add)} permissions to {role_name}')

        # Create a default super admin user if it doesn't exist
        if not User.objects.filter(is_superuser=True).exists():
            admin_user = User.objects.create_superuser(
                username='admin',
                email='<EMAIL>',
                password='admin123',
                first_name='System',
                last_name='Administrator'
            )
            
            # Add to Super Admin role
            super_admin_group = Group.objects.get(name='Super Admin')
            admin_user.groups.add(super_admin_group)
            
            self.stdout.write(
                self.style.SUCCESS(
                    'Created default super admin user (username: admin, password: admin123)'
                )
            )

        # Create URL protection rules
        self._create_url_protection_rules()

        self.stdout.write(
            self.style.SUCCESS('Dynamic RBAC setup completed successfully!')
        )
        
        # Display summary
        self.stdout.write('\nSummary:')
        self.stdout.write(f'- Permission Categories: {PermissionCategory.objects.count()}')
        self.stdout.write(f'- Roles: {Role.objects.count()}')
        self.stdout.write(f'- Role Configurations: {RoleConfiguration.objects.count()}')
        self.stdout.write(f'- URL Protection Rules: {URLProtectionRule.objects.count()}')
        self.stdout.write(f'- Groups: {Group.objects.count()}')
        self.stdout.write(f'- Total Users: {User.objects.count()}')
        self.stdout.write(f'- Admin Users: {User.objects.filter(is_superuser=True).count()}')

    def _get_hierarchy_level(self, role_name):
        """Get hierarchy level for a role"""
        hierarchy_map = {
            'Super Admin': 100,
            'Administrator': 90,
            'Main Registrar': 80,
            'Registrar Officer': 70,
            'Department Head': 60,
            'Verification Clerk': 50,
            'Official Clerk': 40,
            'Service Manager': 30,
            'Applicant': 10,
        }
        return hierarchy_map.get(role_name, 20)

    def _create_url_protection_rules(self):
        """Create URL protection rules for dynamic middleware"""
        self.stdout.write('\nCreating URL protection rules...')

        url_rules = [
            {
                'name': 'Admin API Access',
                'url_pattern': '/api/admin/',
                'required_hierarchy': 90,
                'description': 'Access to admin API endpoints'
            },
            {
                'name': 'User Management API',
                'url_pattern': '/api/user/users/',
                'required_hierarchy': 80,
                'description': 'User management operations'
            },
            {
                'name': 'Role Management API',
                'url_pattern': '/api/user/roles/',
                'required_hierarchy': 90,
                'description': 'Role and permission management'
            },
            {
                'name': 'Permission Management API',
                'url_pattern': '/api/user/permissions/',
                'required_hierarchy': 90,
                'description': 'Permission management operations'
            },
            {
                'name': 'Official Services API',
                'url_pattern': '/api/official/',
                'required_hierarchy': 30,
                'description': 'Official document and service operations'
            },
            {
                'name': 'Staff Registration API',
                'url_pattern': '/api/registration/staff/',
                'required_hierarchy': 30,
                'description': 'Staff-level registration operations'
            }
        ]

        for rule_config in url_rules:
            # Get roles that meet the hierarchy requirement
            required_roles = RoleConfiguration.objects.filter(
                hierarchy_level__gte=rule_config['required_hierarchy'],
                is_active=True
            )

            rule, created = URLProtectionRule.objects.get_or_create(
                url_pattern=rule_config['url_pattern'],
                defaults={
                    'name': rule_config['name'],
                    'is_active': True,
                    'allow_superuser': True,
                    'allowed_methods': []
                }
            )

            # Set required roles
            rule.required_roles.set(required_roles)

            if created:
                self.stdout.write(f'  ✅ Created URL rule: {rule_config["name"]}')
            else:
                self.stdout.write(f'  ℹ️  Updated URL rule: {rule_config["name"]}')

        self.stdout.write(f'Created {len(url_rules)} URL protection rules')
