"""
Dynamic permission system to replace hardcoded role-based permissions
"""
from rest_framework import permissions
from django.core.cache import cache
from django.contrib.auth.models import Permission
from .models import RoleConfiguration, URLProtectionRule, AccessControlCache
import hashlib
import json
from typing import List, Optional, Dict, Any


class DynamicRolePermission(permissions.BasePermission):
    """
    Dynamic permission class that checks roles and permissions from database configuration
    instead of hardcoded values
    """
    
    def __init__(self):
        self.cache_timeout = 300  # 5 minutes
    
    def has_permission(self, request, view):
        """Check if user has permission based on dynamic role configuration"""
        if not request.user or not request.user.is_authenticated:
            return False
        
        # Always allow superusers
        if request.user.is_superuser:
            return True
        
        # Get view-specific requirements
        required_roles = getattr(view, 'required_roles', [])
        required_permissions = getattr(view, 'required_permissions', [])
        require_all_roles = getattr(view, 'require_all_roles', False)
        require_all_permissions = getattr(view, 'require_all_permissions', False)
        
        # If no requirements specified, allow access
        if not required_roles and not required_permissions:
            return True
        
        # Check cached result first
        cache_key = self._get_cache_key(request.user.id, required_roles, required_permissions)
        cached_result = self._get_cached_permission(cache_key)
        if cached_result is not None:
            return cached_result
        
        # Check role requirements
        has_required_roles = self._check_role_requirements(
            request.user, required_roles, require_all_roles
        )
        
        # Check permission requirements
        has_required_permissions = self._check_permission_requirements(
            request.user, required_permissions, require_all_permissions
        )
        
        # Determine final access
        result = has_required_roles and has_required_permissions
        
        # Cache the result
        self._cache_permission(cache_key, result)
        
        return result
    
    def _check_role_requirements(self, user, required_roles: List[str], require_all: bool = False) -> bool:
        """Check if user meets role requirements"""
        if not required_roles:
            return True
        
        user_roles = set(user.groups.values_list('name', flat=True))
        required_roles_set = set(required_roles)
        
        if require_all:
            return required_roles_set.issubset(user_roles)
        else:
            return bool(required_roles_set.intersection(user_roles))
    
    def _check_permission_requirements(self, user, required_permissions: List[str], require_all: bool = False) -> bool:
        """Check if user meets permission requirements"""
        if not required_permissions:
            return True
        
        if require_all:
            return all(user.has_perm(perm) for perm in required_permissions)
        else:
            return any(user.has_perm(perm) for perm in required_permissions)
    
    def _get_cache_key(self, user_id: int, roles: List[str], permissions: List[str]) -> str:
        """Generate cache key for permission check"""
        data = {
            'user_id': user_id,
            'roles': sorted(roles),
            'permissions': sorted(permissions)
        }
        return f"perm_{hashlib.md5(json.dumps(data, sort_keys=True).encode()).hexdigest()}"
    
    def _get_cached_permission(self, cache_key: str) -> Optional[bool]:
        """Get cached permission result"""
        return AccessControlCache.get_cached_value(cache_key)
    
    def _cache_permission(self, cache_key: str, result: bool):
        """Cache permission result"""
        AccessControlCache.set_cached_value(cache_key, result, self.cache_timeout)


class ResourceBasedPermission(DynamicRolePermission):
    """
    Permission class that checks access to specific resources based on role configuration
    """
    
    def has_permission(self, request, view):
        """Check resource-based permissions"""
        if not request.user or not request.user.is_authenticated:
            return False
        
        if request.user.is_superuser:
            return True
        
        # Get resource and action from view
        resource_name = getattr(view, 'resource_name', None)
        action = self._get_action_from_method(request.method)
        
        if not resource_name:
            # Fall back to standard permission check
            return super().has_permission(request, view)
        
        # Check if user's roles can access this resource
        return self._check_resource_access(request.user, resource_name, action)
    
    def _get_action_from_method(self, method: str) -> str:
        """Map HTTP method to action"""
        method_action_map = {
            'GET': 'view',
            'POST': 'add',
            'PUT': 'change',
            'PATCH': 'change',
            'DELETE': 'delete'
        }
        return method_action_map.get(method.upper(), 'view')
    
    def _check_resource_access(self, user, resource_name: str, action: str) -> bool:
        """Check if user can access resource with given action"""
        user_role_names = list(user.groups.values_list('name', flat=True))
        
        # Get role configurations for user's roles
        role_configs = RoleConfiguration.objects.filter(
            display_name__in=user_role_names,
            is_active=True
        )
        
        # Check if any role can access the resource
        for role_config in role_configs:
            if role_config.can_access_resource(resource_name, action):
                return True
        
        return False


class URLBasedPermission(permissions.BasePermission):
    """
    Permission class that uses URLProtectionRule for access control
    """
    
    def has_permission(self, request, view):
        """Check URL-based permissions"""
        if not request.user or not request.user.is_authenticated:
            return False
        
        if request.user.is_superuser:
            return True
        
        url_path = request.path
        method = request.method
        
        # Find matching URL protection rules
        matching_rules = URLProtectionRule.objects.filter(is_active=True)
        
        for rule in matching_rules:
            if rule.matches_url(url_path):
                # Check if method is allowed (if method restrictions exist)
                if rule.allowed_methods and method not in rule.allowed_methods:
                    continue
                
                # Check if user has access according to this rule
                if not rule.user_has_access(request.user):
                    return False
        
        return True


# Convenience permission classes for common use cases
class DynamicAdminPermission(DynamicRolePermission):
    """Dynamic admin permission that checks for admin-level roles"""
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        if request.user.is_superuser:
            return True
        
        # Check for high-level administrative roles
        admin_roles = RoleConfiguration.objects.filter(
            hierarchy_level__gte=80,  # Admin level and above
            is_active=True
        ).values_list('display_name', flat=True)
        
        user_roles = set(request.user.groups.values_list('name', flat=True))
        return bool(set(admin_roles).intersection(user_roles))


class DynamicStaffPermission(DynamicRolePermission):
    """Dynamic staff permission that checks for staff-level roles"""
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        if request.user.is_superuser:
            return True
        
        # Check for staff-level roles
        staff_roles = RoleConfiguration.objects.filter(
            hierarchy_level__gte=30,  # Staff level and above
            is_active=True
        ).values_list('display_name', flat=True)
        
        user_roles = set(request.user.groups.values_list('name', flat=True))
        return bool(set(staff_roles).intersection(user_roles))


# Utility functions for dynamic permission checking
def check_user_has_role_level(user, min_hierarchy_level: int) -> bool:
    """Check if user has a role with at least the specified hierarchy level"""
    if not user or not user.is_authenticated:
        return False

    if user.is_superuser:
        return True

    from django.db import models
    user_role_names = list(user.groups.values_list('name', flat=True))
    max_level = RoleConfiguration.objects.filter(
        display_name__in=user_role_names,
        is_active=True
    ).aggregate(max_level=models.Max('hierarchy_level'))['max_level']

    return max_level is not None and max_level >= min_hierarchy_level


def get_user_max_hierarchy_level(user) -> int:
    """Get the maximum hierarchy level for user's roles"""
    if not user or not user.is_authenticated:
        return 0

    if user.is_superuser:
        return 999  # Superuser has maximum level

    from django.db import models
    user_role_names = list(user.groups.values_list('name', flat=True))
    max_level = RoleConfiguration.objects.filter(
        display_name__in=user_role_names,
        is_active=True
    ).aggregate(max_level=models.Max('hierarchy_level'))['max_level']

    return max_level or 0
