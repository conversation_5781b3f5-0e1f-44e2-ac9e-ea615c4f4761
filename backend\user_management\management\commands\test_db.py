from django.core.management.base import BaseCommand
from django.db import connection
from django.conf import settings
import os


class Command(BaseCommand):
    help = 'Test database connection and configuration'

    def handle(self, *args, **options):
        self.stdout.write('🔧 Testing Database Configuration')
        self.stdout.write('=' * 40)
        
        # Print environment variables
        self.stdout.write('\n📋 Environment Variables:')
        env_vars = [
            'POSTGRES_HOST', 'POSTGRES_PORT', 'POSTGRES_DB', 
            'POSTGRES_USER', 'POSTGRES_PASSWORD',
            'DB_HOST', 'DB_PORT', 'DB_NAME', 'DB_USER', 'DB_PASSWORD'
        ]
        
        for var in env_vars:
            value = os.getenv(var, 'NOT SET')
            if 'PASSWORD' in var and value:
                value = '*' * len(value)
            self.stdout.write(f'  {var}: {value}')
        
        # Print Django database settings
        self.stdout.write('\n🗄️ Django Database Settings:')
        db_config = settings.DATABASES['default']
        for key, value in db_config.items():
            if key == 'PASSWORD' and value:
                value = '*' * len(value)
            self.stdout.write(f'  {key}: {value}')
        
        # Test connection
        self.stdout.write('\n🔌 Testing Database Connection...')
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT version();")
                version = cursor.fetchone()
                self.stdout.write(f'✅ Connection successful!')
                self.stdout.write(f'PostgreSQL version: {version[0]}')
                
                # Test basic operations
                cursor.execute("SELECT current_user, current_database();")
                user_db = cursor.fetchone()
                self.stdout.write(f'Connected as: {user_db[0]} to database: {user_db[1]}')
                
        except Exception as e:
            self.stdout.write(f'❌ Connection failed: {e}')
            
        self.stdout.write('\n' + '=' * 40)
