# Environment Configuration for Online Application Portal
# Copy this file to .env and update the values as needed

# Django Settings
DEBUG=True
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# Database Configuration
USE_DOCKER=false
POSTGRES_DB=online_portal_db
POSTGRES_USER=portal_user
POSTGRES_PASSWORD=portal_password123
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

# Legacy Database Environment Variables (for backward compatibility)
DB_ENGINE=django.db.backends.postgresql
DB_NAME=online_portal_db
DB_USER=portal_user
DB_PASSWORD=portal_password123
DB_HOST=localhost
DB_PORT=5432

# Redis Configuration
REDIS_PASSWORD=redis_password123
REDIS_HOST=localhost
REDIS_PORT=6379

# Email Configuration (optional)
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Security Settings
CSRF_TRUSTED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# File Upload Settings
MEDIA_ROOT=media/
STATIC_ROOT=staticfiles/
MAX_UPLOAD_SIZE=10485760  # 10MB

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/django.log

# Development Settings
DJANGO_SETTINGS_MODULE=backend.settings
