"""
Dynamic middleware to replace hardcoded role-based access control
"""
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin
from django.contrib.auth.models import AnonymousUser
from django.core.cache import cache
from .models import URLProtectionRule, AccessControlCache
import hashlib
import json
import logging

logger = logging.getLogger(__name__)


class DynamicRBACMiddleware(MiddlewareMixin):
    """
    Dynamic middleware that enforces role-based access control using database configuration
    instead of hardcoded rules
    """
    
    def __init__(self, get_response):
        super().__init__(get_response)
        self.cache_timeout = 300  # 5 minutes
        self.rules_cache_key = 'url_protection_rules'
        self.rules_cache_timeout = 600  # 10 minutes
    
    def process_request(self, request):
        """Process request and check URL protection rules"""
        # Skip for non-API requests
        if not request.path.startswith('/api/'):
            return None
        
        # Skip for authentication endpoints
        if self._is_auth_endpoint(request.path):
            return None
        
        # Get active URL protection rules
        rules = self._get_url_protection_rules()
        
        # Check each rule
        for rule in rules:
            if rule.matches_url(request.path):
                # Check if method is allowed
                if rule.allowed_methods and request.method not in rule.allowed_methods:
                    continue
                
                # Check user access
                if not self._check_user_access(request, rule):
                    return self._create_access_denied_response(rule)
        
        return None
    
    def _is_auth_endpoint(self, path):
        """Check if path is an authentication endpoint that should be excluded"""
        auth_endpoints = [
            '/api/token/',
            '/api/token/refresh/',
            '/api/user/register/',
            '/api/csrf/',
            '/api/auth/',
        ]
        return any(path.startswith(endpoint) for endpoint in auth_endpoints)
    
    def _get_url_protection_rules(self):
        """Get URL protection rules with caching"""
        cached_rules = cache.get(self.rules_cache_key)
        if cached_rules is not None:
            return cached_rules
        
        # Fetch active rules from database
        rules = list(URLProtectionRule.objects.filter(is_active=True).prefetch_related(
            'required_roles', 'required_permissions'
        ))
        
        # Cache the rules
        cache.set(self.rules_cache_key, rules, self.rules_cache_timeout)
        
        return rules
    
    def _check_user_access(self, request, rule):
        """Check if user has access according to the rule"""
        # Check authentication
        if not hasattr(request, 'user') or isinstance(request.user, AnonymousUser):
            return False
        
        # Use cached result if available
        cache_key = self._get_access_cache_key(request.user.id, rule.id, request.method)
        cached_result = AccessControlCache.get_cached_value(cache_key)
        if cached_result is not None:
            return cached_result
        
        # Check access using the rule
        has_access = rule.user_has_access(request.user)
        
        # Cache the result
        AccessControlCache.set_cached_value(cache_key, has_access, self.cache_timeout)
        
        return has_access
    
    def _get_access_cache_key(self, user_id, rule_id, method):
        """Generate cache key for access check"""
        data = f"access_{user_id}_{rule_id}_{method}"
        return hashlib.md5(data.encode()).hexdigest()
    
    def _create_access_denied_response(self, rule):
        """Create access denied response"""
        required_roles = list(rule.required_roles.values_list('display_name', flat=True))
        required_permissions = list(rule.required_permissions.values_list('codename', flat=True))
        
        response_data = {
            'error': 'Access denied',
            'message': f'Insufficient permissions to access {rule.url_pattern}',
            'rule_name': rule.name,
            'required_roles': required_roles,
            'required_permissions': required_permissions
        }
        
        logger.warning(f"Access denied for rule: {rule.name} ({rule.url_pattern})")
        
        return JsonResponse(response_data, status=403)


class DynamicRBACHelperMiddleware(MiddlewareMixin):
    """
    Middleware to add dynamic RBAC helper methods to requests
    """
    
    def process_request(self, request):
        """Add RBAC helper methods to request object"""
        # Add dynamic helper methods
        request.has_role = lambda role_name: self._has_role(request.user, role_name)
        request.has_any_role = lambda role_names: self._has_any_role(request.user, role_names)
        request.has_permission = lambda perm: self._has_permission(request.user, perm)
        request.get_user_roles = lambda: self._get_user_roles(request.user)
        request.get_user_permissions = lambda: self._get_user_permissions(request.user)
        request.get_user_hierarchy_level = lambda: self._get_user_hierarchy_level(request.user)
        request.can_access_resource = lambda resource, action='view': self._can_access_resource(
            request.user, resource, action
        )
        
        return None
    
    def _has_role(self, user, role_name):
        """Check if user has a specific role"""
        if not user or not user.is_authenticated:
            return False
        if user.is_superuser:
            return True
        return user.groups.filter(name=role_name).exists()
    
    def _has_any_role(self, user, role_names):
        """Check if user has any of the specified roles"""
        if not user or not user.is_authenticated:
            return False
        if user.is_superuser:
            return True
        return user.groups.filter(name__in=role_names).exists()
    
    def _has_permission(self, user, permission):
        """Check if user has a specific permission"""
        if not user or not user.is_authenticated:
            return False
        if user.is_superuser:
            return True
        return user.has_perm(permission)
    
    def _get_user_roles(self, user):
        """Get list of user's roles"""
        if not user or not user.is_authenticated:
            return []
        return list(user.groups.values_list('name', flat=True))
    
    def _get_user_permissions(self, user):
        """Get list of user's permissions"""
        if not user or not user.is_authenticated:
            return []
        return list(user.get_all_permissions())
    
    def _get_user_hierarchy_level(self, user):
        """Get user's maximum hierarchy level"""
        if not user or not user.is_authenticated:
            return 0
        if user.is_superuser:
            return 999

        from django.db import models
        from .models import RoleConfiguration
        user_role_names = list(user.groups.values_list('name', flat=True))
        max_level = RoleConfiguration.objects.filter(
            display_name__in=user_role_names,
            is_active=True
        ).aggregate(max_level=models.Max('hierarchy_level'))['max_level']

        return max_level or 0
    
    def _can_access_resource(self, user, resource_name, action='view'):
        """Check if user can access a specific resource"""
        if not user or not user.is_authenticated:
            return False
        if user.is_superuser:
            return True
        
        from .models import RoleConfiguration
        user_role_names = list(user.groups.values_list('name', flat=True))
        
        # Get role configurations for user's roles
        role_configs = RoleConfiguration.objects.filter(
            display_name__in=user_role_names,
            is_active=True
        )
        
        # Check if any role can access the resource
        for role_config in role_configs:
            if role_config.can_access_resource(resource_name, action):
                return True
        
        return False


class CacheCleanupMiddleware(MiddlewareMixin):
    """
    Middleware to periodically clean up expired cache entries
    """
    
    def __init__(self, get_response):
        super().__init__(get_response)
        self.cleanup_counter = 0
        self.cleanup_interval = 100  # Clean up every 100 requests
    
    def process_request(self, request):
        """Periodically clean up expired cache entries"""
        self.cleanup_counter += 1
        
        if self.cleanup_counter >= self.cleanup_interval:
            self.cleanup_counter = 0
            try:
                from .models import AccessControlCache
                AccessControlCache.clear_expired()
            except Exception as e:
                logger.error(f"Failed to clean up expired cache entries: {e}")
        
        return None
