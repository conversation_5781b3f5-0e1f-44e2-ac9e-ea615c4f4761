#!/usr/bin/env python
"""
Stop database services for Online Application Portal
"""

import subprocess
import sys

def run_command(command, check=True):
    """Run a shell command"""
    print(f"🔧 Running: {command}")
    try:
        result = subprocess.run(command, shell=True, check=check, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"❌ Command failed: {e}")
        if e.stderr:
            print(f"Error: {e.stderr}")
        return e

def stop_services():
    """Stop all Docker services"""
    print("🛑 Stopping database services...")
    
    # Stop all services
    run_command("docker-compose down")
    
    print("✅ All services stopped!")
    
    # Show status
    print("\n📊 Container status:")
    run_command("docker-compose ps", check=False)

def cleanup_services():
    """Stop and remove all containers, networks, and volumes"""
    print("🧹 Cleaning up all services and data...")
    
    # Stop and remove everything
    run_command("docker-compose down -v --remove-orphans")
    
    print("✅ Cleanup completed!")
    print("⚠️  All database data has been removed!")

def main():
    """Main function"""
    print("🛑 Online Application Portal - Stop Database Services")
    print("="*50)
    
    if len(sys.argv) > 1 and sys.argv[1] == "--cleanup":
        cleanup_services()
    else:
        stop_services()
        print("\n💡 To completely remove all data, run:")
        print("   python stop_database.py --cleanup")

if __name__ == "__main__":
    main()
