#!/usr/bin/env python
"""
Database setup script for Online Application Portal
Starts PostgreSQL with Docker and sets up the Django database
"""

import os
import sys
import subprocess
import time
import psycopg2
from pathlib import Path

def run_command(command, cwd=None, check=True):
    """Run a shell command and return the result"""
    print(f"🔧 Running: {command}")
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd, 
            check=check,
            capture_output=True,
            text=True
        )
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"❌ Command failed: {e}")
        if e.stderr:
            print(f"Error: {e.stderr}")
        if check:
            sys.exit(1)
        return e

def check_docker():
    """Check if Docker is installed and running"""
    print("🐳 Checking Docker...")
    try:
        result = run_command("docker --version", check=False)
        if result.returncode != 0:
            print("❌ Docker is not installed. Please install Docker first.")
            return False
        
        result = run_command("docker info", check=False)
        if result.returncode != 0:
            print("❌ Docker is not running. Please start Docker first.")
            return False
        
        print("✅ Docker is installed and running")
        return True
    except Exception as e:
        print(f"❌ Error checking Docker: {e}")
        return False

def start_database():
    """Start PostgreSQL using Docker Compose"""
    print("🚀 Starting PostgreSQL database...")
    
    # Check if containers are already running
    result = run_command("docker-compose ps postgres", check=False)
    if "Up" in result.stdout:
        print("✅ PostgreSQL is already running")
        return True
    
    # Start the database
    run_command("docker-compose up -d postgres")
    
    # Wait for database to be ready
    print("⏳ Waiting for database to be ready...")
    max_attempts = 30
    for attempt in range(max_attempts):
        try:
            # Try to connect to the database
            conn = psycopg2.connect(
                host="localhost",
                port="5432",
                database="online_portal_db",
                user="portal_user",
                password="portal_password123"
            )
            conn.close()
            print("✅ Database is ready!")
            return True
        except psycopg2.OperationalError:
            if attempt < max_attempts - 1:
                print(f"⏳ Attempt {attempt + 1}/{max_attempts} - waiting...")
                time.sleep(2)
            else:
                print("❌ Database failed to start after 60 seconds")
                return False
    
    return False

def setup_django():
    """Set up Django database and run migrations"""
    print("🔧 Setting up Django database...")
    
    backend_dir = Path(__file__).parent / "backend"
    
    # Install requirements if needed
    print("📦 Installing Python requirements...")
    run_command("pip install -r requirements.txt", cwd=backend_dir)
    
    # Run migrations
    print("🔄 Running database migrations...")
    run_command("python manage.py makemigrations", cwd=backend_dir)
    run_command("python manage.py migrate", cwd=backend_dir)
    
    # Set up RBAC system
    print("🛡️ Setting up RBAC system...")
    run_command("python manage.py setup_rbac", cwd=backend_dir)
    
    # Create superuser if needed
    print("👤 Creating superuser...")
    create_superuser_script = """
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
if not User.objects.filter(is_superuser=True).exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('Superuser created: admin/admin123')
else:
    print('Superuser already exists')
"""
    
    with open(backend_dir / "create_superuser.py", "w") as f:
        f.write(create_superuser_script)
    
    run_command("python create_superuser.py", cwd=backend_dir)
    
    # Clean up
    os.remove(backend_dir / "create_superuser.py")
    
    print("✅ Django setup completed!")

def start_services():
    """Start additional services (pgAdmin, Redis)"""
    print("🚀 Starting additional services...")
    run_command("docker-compose up -d")
    print("✅ All services started!")
    
    print("\n" + "="*50)
    print("🎉 DATABASE SETUP COMPLETED!")
    print("="*50)
    print("📊 Service URLs:")
    print("  🐘 PostgreSQL: localhost:5432")
    print("  🔧 pgAdmin: http://localhost:8080")
    print("     Email: <EMAIL>")
    print("     Password: admin123")
    print("  🔴 Redis: localhost:6379")
    print("\n📋 Database Credentials:")
    print("  Database: online_portal_db")
    print("  User: portal_user")
    print("  Password: portal_password123")
    print("\n👤 Django Admin:")
    print("  Username: admin")
    print("  Password: admin123")
    print("="*50)

def main():
    """Main setup function"""
    print("🚀 Online Application Portal - Database Setup")
    print("="*50)
    
    # Check prerequisites
    if not check_docker():
        sys.exit(1)
    
    # Start database
    if not start_database():
        sys.exit(1)
    
    # Setup Django
    try:
        setup_django()
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        sys.exit(1)
    
    # Start all services
    start_services()
    
    print("\n🎯 Next steps:")
    print("1. Start the Django development server:")
    print("   cd backend && python manage.py runserver")
    print("2. Start the frontend development server:")
    print("   cd frontend && npm start")
    print("3. Access pgAdmin at http://localhost:8080 to manage the database")

if __name__ == "__main__":
    main()
