from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from user_management.models import RoleConfiguration, URLProtectionRule
import re


class Command(BaseCommand):
    help = 'Migrate existing hardcoded roles to dynamic RBAC system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be migrated without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        
        self.stdout.write('Starting migration to dynamic RBAC system...')
        
        # Migrate existing groups to RoleConfiguration
        self.migrate_groups_to_role_configs(dry_run)
        
        # Create URL protection rules from hardcoded middleware rules
        self.create_url_protection_rules(dry_run)
        
        self.stdout.write(self.style.SUCCESS('Migration completed successfully!'))

    def migrate_groups_to_role_configs(self, dry_run):
        """Migrate existing Django groups to RoleConfiguration"""
        self.stdout.write('\n📋 Migrating existing groups to RoleConfiguration...')
        
        # Define role hierarchy and descriptions
        role_hierarchy = {
            'Super Admin': {
                'hierarchy_level': 100,
                'description': 'Highest level access with full system control',
                'is_system_role': True
            },
            'Administrator': {
                'hierarchy_level': 90,
                'description': 'Administrative access to most system functions',
                'is_system_role': True
            },
            'Main Registrar': {
                'hierarchy_level': 80,
                'description': 'Primary registrar with full registration management access'
            },
            'Registrar Officer': {
                'hierarchy_level': 70,
                'description': 'Registrar staff with registration processing access'
            },
            'Department Head': {
                'hierarchy_level': 60,
                'description': 'Department management and oversight access'
            },
            'Verification Clerk': {
                'hierarchy_level': 50,
                'description': 'Document verification and processing access'
            },
            'Official Clerk': {
                'hierarchy_level': 40,
                'description': 'Official document processing access'
            },
            'Service Manager': {
                'hierarchy_level': 30,
                'description': 'Service management and coordination access'
            },
            'Applicant': {
                'hierarchy_level': 10,
                'description': 'End user access for application submission'
            }
        }
        
        groups = Group.objects.all()
        migrated_count = 0
        
        for group in groups:
            role_info = role_hierarchy.get(group.name, {
                'hierarchy_level': 20,
                'description': f'Role for {group.name}',
                'is_system_role': False
            })
            
            if not dry_run:
                role_config, created = RoleConfiguration.objects.get_or_create(
                    name=group.name.lower().replace(' ', '_'),
                    defaults={
                        'display_name': group.name,
                        'description': role_info['description'],
                        'hierarchy_level': role_info['hierarchy_level'],
                        'is_system_role': role_info.get('is_system_role', False),
                        'is_active': True
                    }
                )
                
                # Add permissions from the group
                role_config.permissions.set(group.permissions.all())
                
                if created:
                    self.stdout.write(f'  ✅ Created RoleConfiguration: {group.name}')
                else:
                    self.stdout.write(f'  ℹ️  Updated RoleConfiguration: {group.name}')
            else:
                self.stdout.write(f'  📝 Would migrate: {group.name} (Level: {role_info["hierarchy_level"]})')
            
            migrated_count += 1
        
        self.stdout.write(f'Migrated {migrated_count} groups to RoleConfiguration')

    def create_url_protection_rules(self, dry_run):
        """Create URL protection rules from hardcoded middleware rules"""
        self.stdout.write('\n🔒 Creating URL protection rules...')
        
        # Hardcoded rules from middleware (to be replaced)
        hardcoded_rules = {
            '/api/admin/': {
                'name': 'Admin API Access',
                'required_roles': ['Administrator', 'Super Admin'],
                'description': 'Access to admin API endpoints'
            },
            '/api/user/users/': {
                'name': 'User Management API',
                'required_roles': ['Administrator', 'Super Admin', 'Manager'],
                'description': 'User management operations'
            },
            '/api/user/roles/': {
                'name': 'Role Management API',
                'required_roles': ['Administrator', 'Super Admin'],
                'description': 'Role and permission management'
            },
            '/api/user/permissions/': {
                'name': 'Permission Management API',
                'required_roles': ['Administrator', 'Super Admin'],
                'description': 'Permission management operations'
            },
            '/api/official/': {
                'name': 'Official Services API',
                'required_roles': ['Administrator', 'Super Admin', 'Manager', 'Staff'],
                'description': 'Official document and service operations'
            },
            '/api/registration/staff/': {
                'name': 'Staff Registration API',
                'required_roles': ['Administrator', 'Super Admin', 'Manager', 'Staff'],
                'description': 'Staff-level registration operations'
            }
        }
        
        created_count = 0
        
        for url_pattern, rule_info in hardcoded_rules.items():
            if not dry_run:
                # Convert role names to RoleConfiguration objects
                role_configs = RoleConfiguration.objects.filter(
                    display_name__in=rule_info['required_roles']
                )
                
                rule, created = URLProtectionRule.objects.get_or_create(
                    url_pattern=url_pattern,
                    defaults={
                        'name': rule_info['name'],
                        'is_active': True,
                        'allow_superuser': True,
                        'allowed_methods': []  # Apply to all methods
                    }
                )
                
                # Set required roles
                rule.required_roles.set(role_configs)
                
                if created:
                    self.stdout.write(f'  ✅ Created URL rule: {rule_info["name"]} ({url_pattern})')
                else:
                    self.stdout.write(f'  ℹ️  Updated URL rule: {rule_info["name"]} ({url_pattern})')
            else:
                self.stdout.write(f'  📝 Would create: {rule_info["name"]} for {url_pattern}')
                self.stdout.write(f'      Required roles: {", ".join(rule_info["required_roles"])}')
            
            created_count += 1
        
        self.stdout.write(f'Created {created_count} URL protection rules')
