-- Initialize the database for Online Application Portal
-- This script runs when the PostgreSQL container starts for the first time

-- Create additional databases if needed
-- CREATE DATABASE online_portal_test;

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE online_portal_db TO portal_user;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Set timezone
SET timezone = 'UTC';

-- Create initial schema if needed
-- You can add any initial database setup here

-- Log the initialization
DO $$
BEGIN
    RAISE NOTICE 'Online Application Portal database initialized successfully';
END $$;
