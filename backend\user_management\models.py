from django.db import models
from django.contrib.auth.models import User, Group, Permission
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.core.cache import cache
import uuid
import json

# Extended User Profile for RBAC
class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    department = models.CharField(max_length=100, blank=True, null=True)
    phone = models.CharField(max_length=20, blank=True, null=True)
    employee_id = models.Char<PERSON>ield(max_length=50, blank=True, null=True, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}'s profile"

    @property
    def full_name(self):
        return f"{self.user.first_name} {self.user.last_name}".strip()

    @property
    def roles(self):
        """Get all roles (groups) assigned to this user"""
        return self.user.groups.all()

    @property
    def permissions(self):
        """Get all permissions for this user (direct + group permissions)"""
        return self.user.get_all_permissions()

    def has_role(self, role_name):
        """Check if user has a specific role"""
        return self.user.groups.filter(name=role_name).exists()

    def has_any_role(self, role_names):
        """Check if user has any of the specified roles"""
        return self.user.groups.filter(name__in=role_names).exists()

    def add_role(self, role_name):
        """Add a role to the user"""
        try:
            role = Group.objects.get(name=role_name)
            self.user.groups.add(role)
            return True
        except Group.DoesNotExist:
            return False

    def remove_role(self, role_name):
        """Remove a role from the user"""
        try:
            role = Group.objects.get(name=role_name)
            self.user.groups.remove(role)
            return True
        except Group.DoesNotExist:
            return False

# Signal to create UserProfile when User is created
@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    if created:
        UserProfile.objects.create(user=instance)

@receiver(post_save, sender=User)
def save_user_profile(sender, instance, **kwargs):
    if hasattr(instance, 'profile'):
        instance.profile.save()
    else:
        UserProfile.objects.create(user=instance)

# Custom Role model for enhanced role management
class Role(models.Model):
    """
    Custom Role model that extends Django's Group model functionality
    """
    group = models.OneToOneField(Group, on_delete=models.CASCADE, related_name='role_detail')
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    def __str__(self):
        return self.group.name

    @property
    def name(self):
        return self.group.name

    @property
    def permissions(self):
        return self.group.permissions.all()

    def add_permission(self, permission_codename):
        """Add a permission to this role"""
        try:
            permission = Permission.objects.get(codename=permission_codename)
            self.group.permissions.add(permission)
            return True
        except Permission.DoesNotExist:
            return False

    def remove_permission(self, permission_codename):
        """Remove a permission from this role"""
        try:
            permission = Permission.objects.get(codename=permission_codename)
            self.group.permissions.remove(permission)
            return True
        except Permission.DoesNotExist:
            return False

# Permission Categories for better organization
class PermissionCategory(models.Model):
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "Permission Categories"

    def __str__(self):
        return self.name


# Dynamic Role Configuration System
class RoleConfiguration(models.Model):
    """
    Configuration model for dynamic role-based access control
    """
    name = models.CharField(max_length=100, unique=True, help_text="Unique identifier for this role configuration")
    display_name = models.CharField(max_length=200, help_text="Human-readable name for this role")
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    is_system_role = models.BooleanField(default=False, help_text="System roles cannot be deleted")
    hierarchy_level = models.IntegerField(default=0, help_text="Higher numbers indicate higher privilege levels")

    # Configuration fields
    permissions = models.ManyToManyField(Permission, blank=True, help_text="Permissions granted by this role")
    inherits_from = models.ManyToManyField('self', blank=True, symmetrical=False, help_text="Roles this role inherits permissions from")

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        ordering = ['-hierarchy_level', 'name']

    def __str__(self):
        return self.display_name or self.name

    def get_all_permissions(self):
        """Get all permissions including inherited ones"""
        permissions = set(self.permissions.all())

        # Add inherited permissions
        for parent_role in self.inherits_from.all():
            permissions.update(parent_role.get_all_permissions())

        return list(permissions)

    def can_access_resource(self, resource_name, action='view'):
        """Check if this role can access a specific resource with given action"""
        permission_codename = f"{action}_{resource_name}"
        all_permissions = self.get_all_permissions()
        return any(perm.codename == permission_codename for perm in all_permissions)


class URLProtectionRule(models.Model):
    """
    Dynamic URL protection rules to replace hardcoded middleware rules
    """
    name = models.CharField(max_length=200, help_text="Descriptive name for this rule")
    url_pattern = models.CharField(max_length=500, help_text="URL pattern to protect (supports regex)")
    required_roles = models.ManyToManyField(RoleConfiguration, blank=True, help_text="Roles that can access this URL")
    required_permissions = models.ManyToManyField(Permission, blank=True, help_text="Permissions required to access this URL")
    is_active = models.BooleanField(default=True)
    allow_superuser = models.BooleanField(default=True, help_text="Allow superusers to bypass this rule")

    # HTTP method restrictions
    allowed_methods = models.JSONField(default=list, blank=True, help_text="HTTP methods this rule applies to (empty = all methods)")

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.url_pattern})"

    def matches_url(self, url_path):
        """Check if this rule matches the given URL path"""
        import re
        try:
            return bool(re.match(self.url_pattern, url_path))
        except re.error:
            # If regex is invalid, fall back to simple string matching
            return url_path.startswith(self.url_pattern)

    def user_has_access(self, user):
        """Check if user has access according to this rule"""
        if not user or not user.is_authenticated:
            return False

        if self.allow_superuser and user.is_superuser:
            return True

        # Check role requirements
        if self.required_roles.exists():
            user_role_names = list(user.groups.values_list('name', flat=True))
            required_role_names = list(self.required_roles.values_list('name', flat=True))
            if not any(role in user_role_names for role in required_role_names):
                return False

        # Check permission requirements
        if self.required_permissions.exists():
            required_perms = list(self.required_permissions.values_list('codename', flat=True))
            if not all(user.has_perm(f"auth.{perm}") or user.has_perm(perm) for perm in required_perms):
                return False

        return True


class AccessControlCache(models.Model):
    """
    Cache model for storing computed access control decisions
    """
    cache_key = models.CharField(max_length=255, unique=True)
    cache_value = models.JSONField()
    expires_at = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['cache_key']),
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        return f"Cache: {self.cache_key}"

    @classmethod
    def get_cached_value(cls, key):
        """Get cached value if not expired"""
        from django.utils import timezone
        try:
            cache_obj = cls.objects.get(cache_key=key, expires_at__gt=timezone.now())
            return cache_obj.cache_value
        except cls.DoesNotExist:
            return None

    @classmethod
    def set_cached_value(cls, key, value, ttl_seconds=300):
        """Set cached value with TTL"""
        from django.utils import timezone
        expires_at = timezone.now() + timezone.timedelta(seconds=ttl_seconds)
        cls.objects.update_or_create(
            cache_key=key,
            defaults={'cache_value': value, 'expires_at': expires_at}
        )

    @classmethod
    def clear_expired(cls):
        """Clear expired cache entries"""
        from django.utils import timezone
        cls.objects.filter(expires_at__lt=timezone.now()).delete()
