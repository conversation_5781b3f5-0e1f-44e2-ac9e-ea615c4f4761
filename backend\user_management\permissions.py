from rest_framework import permissions
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from .dynamic_permissions import (
    DynamicRolePermission,
    DynamicAdminPermission,
    DynamicStaffPermission,
    ResourceBasedPermission,
    URLBasedPermission,
    check_user_has_role_level
)


# Legacy permission class for backward compatibility
class RoleBasedPermission(DynamicRolePermission):
    """
    Legacy permission class - now uses dynamic role checking
    Kept for backward compatibility but uses the new dynamic system
    """
    required_roles = []  # Override in subclasses
    required_permissions = []  # Override in subclasses
    allow_superuser = True  # Allow superusers by default

    def has_permission(self, request, view):
        # Set view attributes for dynamic permission checking
        if self.required_roles:
            view.required_roles = self.required_roles
        if self.required_permissions:
            view.required_permissions = self.required_permissions

        # Use dynamic permission checking
        return super().has_permission(request, view)


# Updated permission classes using dynamic system
class AdminOnlyPermission(DynamicAdminPermission):
    """Permission for admin-only access - now uses dynamic role hierarchy"""
    pass


class StaffOnlyPermission(DynamicStaffPermission):
    """Permission for staff-only access - now uses dynamic role hierarchy"""
    pass


class RegistrarPermission(permissions.BasePermission):
    """Permission for registrar-level access - now uses dynamic hierarchy"""

    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False

        if request.user.is_superuser:
            return True

        # Check for registrar-level access (hierarchy level 70+)
        return check_user_has_role_level(request.user, 70)


class DepartmentHeadPermission(permissions.BasePermission):
    """Permission for department head access - now uses dynamic hierarchy"""

    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False

        if request.user.is_superuser:
            return True

        # Check for department head level access (hierarchy level 60+)
        return check_user_has_role_level(request.user, 60)


class UserManagementPermission(RoleBasedPermission):
    """Permission for user management operations"""
    required_permissions = [
        'auth.view_user',
        'auth.add_user',
        'auth.change_user',
        'auth.delete_user'
    ]


class RoleManagementPermission(RoleBasedPermission):
    """Permission for role management operations"""
    required_permissions = [
        'auth.view_group',
        'auth.add_group',
        'auth.change_group',
        'auth.delete_group'
    ]


class RegistrationManagementPermission(RoleBasedPermission):
    """Permission for registration management"""
    required_permissions = [
        'registration.view_applicantprogramselection',
        'registration.add_applicantprogramselection',
        'registration.change_applicantprogramselection',
        'registration.delete_applicantprogramselection'
    ]


class OfficialCertificatePermission(RoleBasedPermission):
    """Permission for official certificate management"""
    required_permissions = [
        'official.view_official',
        'official.add_official',
        'official.change_official',
        'official.delete_official'
    ]


class DynamicRolePermission(permissions.BasePermission):
    """
    Dynamic permission class that can be configured per view
    """
    def __init__(self, required_roles=None, required_permissions=None, allow_superuser=True):
        self.required_roles = required_roles or []
        self.required_permissions = required_permissions or []
        self.allow_superuser = allow_superuser

    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False

        if self.allow_superuser and request.user.is_superuser:
            return True

        if self.required_roles:
            user_roles = request.user.groups.values_list('name', flat=True)
            if not any(role in user_roles for role in self.required_roles):
                return False

        if self.required_permissions:
            if not all(request.user.has_perm(perm) for perm in self.required_permissions):
                return False

        return True


class MethodBasedPermission(permissions.BasePermission):
    """
    Permission class that applies different rules based on HTTP method
    Now uses hierarchy levels instead of hardcoded role names
    """
    def __init__(self, hierarchy_map=None):
        """
        hierarchy_map example:
        {
            'GET': 10,    # Applicant level and above
            'POST': 60,   # Department Head level and above
            'PUT': 80,    # Main Registrar level and above
            'DELETE': 90  # Administrator level and above
        }
        """
        self.hierarchy_map = hierarchy_map or {}

    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False

        if request.user.is_superuser:
            return True

        method = request.method
        required_level = self.hierarchy_map.get(method, 0)

        if required_level == 0:
            return True  # No specific requirements for this method

        return check_user_has_role_level(request.user, required_level)


def has_role(user, role_name):
    """Helper function to check if user has a specific role"""
    if not user or not user.is_authenticated:
        return False
    return user.groups.filter(name=role_name).exists()


def has_any_role(user, role_names):
    """Helper function to check if user has any of the specified roles"""
    if not user or not user.is_authenticated:
        return False
    return user.groups.filter(name__in=role_names).exists()


def has_permission(user, permission_codename):
    """Helper function to check if user has a specific permission"""
    if not user or not user.is_authenticated:
        return False
    return user.has_perm(permission_codename)


def get_user_roles(user):
    """Helper function to get all roles for a user"""
    if not user or not user.is_authenticated:
        return []
    return list(user.groups.values_list('name', flat=True))


def get_user_permissions(user):
    """Helper function to get all permissions for a user"""
    if not user or not user.is_authenticated:
        return []
    return list(user.get_all_permissions())


class ResourceOwnerPermission(permissions.BasePermission):
    """
    Permission that allows access only to resource owners or admins
    Now uses dynamic hierarchy levels instead of hardcoded roles
    """
    def has_object_permission(self, request, view, obj):
        # Allow superusers
        if request.user.is_superuser:
            return True

        # Allow admin-level users (hierarchy 90+)
        if check_user_has_role_level(request.user, 90):
            return True

        # Check if user owns the resource
        if hasattr(obj, 'user') and obj.user == request.user:
            return True

        if hasattr(obj, 'author') and obj.author == request.user:
            return True

        if hasattr(obj, 'created_by') and obj.created_by == request.user:
            return True

        return False


class DepartmentBasedPermission(permissions.BasePermission):
    """
    Permission that restricts access based on department
    Now uses dynamic hierarchy levels instead of hardcoded roles
    """
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False

        if request.user.is_superuser:
            return True

        # Check if user has profile and department
        if hasattr(request.user, 'profile') and request.user.profile.department:
            return True

        # Allow users with admin-level roles regardless of department (hierarchy 90+)
        return check_user_has_role_level(request.user, 90)

    def has_object_permission(self, request, view, obj):
        if not request.user or not request.user.is_authenticated:
            return False

        if request.user.is_superuser:
            return True

        # Allow admin-level users (hierarchy 90+)
        if check_user_has_role_level(request.user, 90):
            return True

        # Check department match
        if hasattr(request.user, 'profile') and hasattr(obj, 'department'):
            return request.user.profile.department == obj.department

        return False
