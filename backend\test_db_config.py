#!/usr/bin/env python
"""
Test database configuration
"""

import os
import sys
from pathlib import Path

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
from dotenv import load_dotenv
load_dotenv(Path(__file__).parent.parent / '.env')

print("🔧 Database Configuration Test")
print("=" * 40)

print("\n📋 Environment Variables:")
print(f"POSTGRES_HOST: {os.getenv('POSTGRES_HOST', 'NOT SET')}")
print(f"POSTGRES_PORT: {os.getenv('POSTGRES_PORT', 'NOT SET')}")
print(f"POSTGRES_DB: {os.getenv('POSTGRES_DB', 'NOT SET')}")
print(f"POSTGRES_USER: {os.getenv('POSTGRES_USER', 'NOT SET')}")
print(f"POSTGRES_PASSWORD: {os.getenv('POSTGRES_PASSWORD', 'NOT SET')}")

print(f"\nDB_HOST: {os.getenv('DB_HOST', 'NOT SET')}")
print(f"DB_PORT: {os.getenv('DB_PORT', 'NOT SET')}")
print(f"DB_NAME: {os.getenv('DB_NAME', 'NOT SET')}")
print(f"DB_USER: {os.getenv('DB_USER', 'NOT SET')}")
print(f"DB_PASSWORD: {os.getenv('DB_PASSWORD', 'NOT SET')}")

# Test database configuration
try:
    from database_config import get_database_config
    db_config = get_database_config()
    print("\n🗄️ Database Configuration:")
    print(f"ENGINE: {db_config['default']['ENGINE']}")
    print(f"NAME: {db_config['default']['NAME']}")
    print(f"USER: {db_config['default']['USER']}")
    print(f"HOST: {db_config['default']['HOST']}")
    print(f"PORT: {db_config['default']['PORT']}")
    print(f"PASSWORD: {'*' * len(db_config['default']['PASSWORD'])}")
except Exception as e:
    print(f"❌ Error loading database config: {e}")

# Test direct connection
print("\n🔌 Testing Direct Connection...")
try:
    import psycopg2
    
    # Try with environment variables
    host = os.getenv('POSTGRES_HOST', '127.0.0.1')
    port = os.getenv('POSTGRES_PORT', '5432')
    database = os.getenv('POSTGRES_DB', 'online_portal_db')
    user = os.getenv('POSTGRES_USER', 'portal_user')
    password = os.getenv('POSTGRES_PASSWORD', 'portal_password123')
    
    print(f"Connecting to: {user}@{host}:{port}/{database}")
    
    conn = psycopg2.connect(
        host=host,
        port=port,
        database=database,
        user=user,
        password=password
    )
    
    cursor = conn.cursor()
    cursor.execute("SELECT version();")
    version = cursor.fetchone()
    print(f"✅ Connection successful!")
    print(f"PostgreSQL version: {version[0]}")
    
    cursor.close()
    conn.close()
    
except Exception as e:
    print(f"❌ Connection failed: {e}")

print("\n" + "=" * 40)
