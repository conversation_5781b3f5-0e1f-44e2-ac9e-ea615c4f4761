# Database Setup Guide

This guide will help you set up PostgreSQL using Docker for the Online Application Portal project.

## Prerequisites

1. **Docker Desktop** - Download and install from [docker.com](https://www.docker.com/products/docker-desktop/)
2. **Python 3.8+** - Make sure Python is installed
3. **psycopg2** - Install with `pip install psycopg2-binary`

## Quick Start

### 1. Start Database Services

Run the automated setup script:

```bash
python setup_database.py
```

This script will:
- ✅ Check if Docker is installed and running
- 🐘 Start PostgreSQL database
- 🔧 Run Django migrations
- 🛡️ Set up the dynamic RBAC system
- 👤 Create a superuser account
- 🚀 Start additional services (pgAdmin, Redis)

### 2. Manual Setup (Alternative)

If you prefer manual setup:

```bash
# Start PostgreSQL
docker-compose up -d postgres

# Wait for database to be ready (about 30 seconds)
docker-compose logs postgres

# Run Django setup
cd backend
python manage.py migrate
python manage.py setup_rbac
python manage.py createsuperuser
```

### 3. Start All Services

```bash
# Start all services (PostgreSQL, pgAdmin, Redis)
docker-compose up -d

# Check status
docker-compose ps
```

## Service Information

### PostgreSQL Database
- **Host**: localhost
- **Port**: 5432
- **Database**: online_portal_db
- **Username**: portal_user
- **Password**: portal_password123

### pgAdmin (Database Management)
- **URL**: http://localhost:8080
- **Email**: <EMAIL>
- **Password**: admin123

### Redis Cache
- **Host**: localhost
- **Port**: 6379
- **Password**: redis_password123

### Django Admin
- **Username**: admin
- **Password**: admin123

## Configuration

### Environment Variables

Copy `.env.example` to `.env` and modify as needed:

```bash
cp .env.example .env
```

Key variables:
- `USE_DOCKER=false` - Set to `true` when running Django in Docker
- `POSTGRES_HOST=localhost` - Change to `postgres` when using Docker
- `DEBUG=True` - Set to `False` in production

### Django Settings

The database configuration is automatically handled by `database_config.py`:

- **Local Development**: Connects to localhost:5432
- **Docker Environment**: Connects to postgres:5432 (container name)

## Common Commands

### Start Services
```bash
# Start all services
docker-compose up -d

# Start only PostgreSQL
docker-compose up -d postgres

# View logs
docker-compose logs postgres
```

### Stop Services
```bash
# Stop all services
python stop_database.py

# Or manually
docker-compose down

# Stop and remove all data
python stop_database.py --cleanup
```

### Django Commands
```bash
cd backend

# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Setup RBAC system
python manage.py setup_rbac

# Start development server
python manage.py runserver
```

### Database Management
```bash
# Connect to PostgreSQL
docker exec -it online_portal_postgres psql -U portal_user -d online_portal_db

# Backup database
docker exec online_portal_postgres pg_dump -U portal_user online_portal_db > backup.sql

# Restore database
docker exec -i online_portal_postgres psql -U portal_user -d online_portal_db < backup.sql
```

## Troubleshooting

### Database Connection Issues

1. **Check if PostgreSQL is running**:
   ```bash
   docker-compose ps postgres
   ```

2. **Check PostgreSQL logs**:
   ```bash
   docker-compose logs postgres
   ```

3. **Test connection**:
   ```bash
   docker exec online_portal_postgres pg_isready -U portal_user
   ```

### Permission Issues

1. **Reset database**:
   ```bash
   docker-compose down -v
   docker-compose up -d postgres
   ```

2. **Check file permissions**:
   ```bash
   ls -la database/init/
   ```

### Port Conflicts

If port 5432 is already in use:

1. **Change port in docker-compose.yml**:
   ```yaml
   ports:
     - "5433:5432"  # Use port 5433 instead
   ```

2. **Update .env file**:
   ```
   POSTGRES_PORT=5433
   ```

## Development Workflow

### Daily Development
1. Start services: `docker-compose up -d`
2. Start Django: `cd backend && python manage.py runserver`
3. Start frontend: `cd frontend && npm start`

### After Code Changes
1. Run migrations: `python manage.py migrate`
2. Restart Django server

### Testing
1. Run tests: `python manage.py test`
2. Check RBAC: `python manage.py migrate_to_dynamic_rbac --dry-run`

## Production Considerations

1. **Change default passwords** in production
2. **Use environment variables** for sensitive data
3. **Enable SSL/TLS** for database connections
4. **Set up database backups**
5. **Configure monitoring** and logging

## Support

If you encounter issues:

1. Check the logs: `docker-compose logs`
2. Verify Docker is running: `docker info`
3. Check database connectivity: `python setup_database.py`
4. Review Django settings: `python manage.py check`

For more help, refer to the main project documentation.
