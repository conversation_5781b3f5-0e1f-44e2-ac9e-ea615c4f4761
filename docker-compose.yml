version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: online_portal_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: online_portal_db
      POSTGRES_USER: portal_user
      POSTGRES_PASSWORD: portal_password123
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - portal_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U portal_user -d online_portal_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # pgAdmin for database management (optional)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: online_portal_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - portal_network
    depends_on:
      postgres:
        condition: service_healthy

  # Redis for caching (optional, for future use)
  redis:
    image: redis:7-alpine
    container_name: online_portal_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - portal_network
    command: redis-server --appendonly yes --requirepass redis_password123

volumes:
  postgres_data:
    driver: local
  pgadmin_data:
    driver: local
  redis_data:
    driver: local

networks:
  portal_network:
    driver: bridge
