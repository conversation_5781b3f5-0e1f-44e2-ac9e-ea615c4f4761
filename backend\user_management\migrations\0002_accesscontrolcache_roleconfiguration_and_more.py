# Generated by Django 5.2.1 on 2025-06-28 03:38

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
        ("user_management", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AccessControlCache",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("cache_key", models.CharField(max_length=255, unique=True)),
                ("cache_value", models.J<PERSON>NField()),
                ("expires_at", models.DateTimeField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["cache_key"], name="user_manage_cache_k_b11b46_idx"
                    ),
                    models.Index(
                        fields=["expires_at"], name="user_manage_expires_ed102e_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="RoleConfiguration",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Unique identifier for this role configuration",
                        max_length=100,
                        unique=True,
                    ),
                ),
                (
                    "display_name",
                    models.CharField(
                        help_text="Human-readable name for this role", max_length=200
                    ),
                ),
                ("description", models.TextField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "is_system_role",
                    models.BooleanField(
                        default=False, help_text="System roles cannot be deleted"
                    ),
                ),
                (
                    "hierarchy_level",
                    models.IntegerField(
                        default=0,
                        help_text="Higher numbers indicate higher privilege levels",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "inherits_from",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Roles this role inherits permissions from",
                        to="user_management.roleconfiguration",
                    ),
                ),
                (
                    "permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Permissions granted by this role",
                        to="auth.permission",
                    ),
                ),
            ],
            options={
                "ordering": ["-hierarchy_level", "name"],
            },
        ),
        migrations.CreateModel(
            name="URLProtectionRule",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Descriptive name for this rule", max_length=200
                    ),
                ),
                (
                    "url_pattern",
                    models.CharField(
                        help_text="URL pattern to protect (supports regex)",
                        max_length=500,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "allow_superuser",
                    models.BooleanField(
                        default=True, help_text="Allow superusers to bypass this rule"
                    ),
                ),
                (
                    "allowed_methods",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="HTTP methods this rule applies to (empty = all methods)",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "required_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Permissions required to access this URL",
                        to="auth.permission",
                    ),
                ),
                (
                    "required_roles",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Roles that can access this URL",
                        to="user_management.roleconfiguration",
                    ),
                ),
            ],
            options={
                "ordering": ["name"],
            },
        ),
    ]
